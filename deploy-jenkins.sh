#!/bin/bash

# Jenkins Docker Compose 部署脚本
# 适用于 Rocky Linux 系统

set -e

echo "=== Jenkins Docker Compose 部署脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户或具有sudo权限
check_privileges() {
    if [[ $EUID -eq 0 ]]; then
        log_info "以root用户运行"
    elif sudo -n true 2>/dev/null; then
        log_info "具有sudo权限"
    else
        log_error "需要root权限或sudo权限来执行此脚本"
        exit 1
    fi
}

# 检查Docker是否安装
check_docker() {
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装: $(docker --version)"
    else
        log_error "Docker 未安装，请先安装Docker"
        exit 1
    fi
}

# 检查Docker Compose是否安装
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose 已安装: $(docker-compose --version)"
    else
        log_error "Docker Compose 未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    # 创建Jenkins数据目录
    sudo mkdir -p /opt/jenkins/jenkins_home
    sudo mkdir -p ./jenkins-backup
    sudo mkdir -p ./nginx/logs
    sudo mkdir -p ./nginx/ssl
    
    # 设置权限
    sudo chown -R 1000:1000 /opt/jenkins/jenkins_home
    sudo chmod -R 755 /opt/jenkins/jenkins_home
    
    log_info "目录创建完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    if command -v firewall-cmd &> /dev/null; then
        sudo firewall-cmd --permanent --add-port=8080/tcp
        sudo firewall-cmd --permanent --add-port=50000/tcp
        sudo firewall-cmd --permanent --add-port=80/tcp
        sudo firewall-cmd --permanent --add-port=443/tcp
        sudo firewall-cmd --reload
        log_info "防火墙规则配置完成"
    else
        log_warn "firewall-cmd 未找到，请手动配置防火墙"
    fi
}

# 启动Jenkins服务
start_jenkins() {
    log_info "启动Jenkins服务..."
    
    # 选择配置文件
    if [[ "$1" == "simple" ]]; then
        COMPOSE_FILE="docker-compose-simple.yml"
        log_info "使用简化配置启动Jenkins"
    else
        COMPOSE_FILE="docker-compose.yml"
        log_info "使用完整配置启动Jenkins"
    fi
    
    # 启动服务
    docker-compose -f $COMPOSE_FILE up -d
    
    log_info "Jenkins 服务启动中..."
    log_info "请等待约2-3分钟让Jenkins完全启动"
}

# 获取初始管理员密码
get_admin_password() {
    log_info "等待Jenkins启动..."
    sleep 60
    
    log_info "获取Jenkins初始管理员密码..."
    
    # 尝试从容器中获取密码
    if docker exec jenkins cat /var/jenkins_home/secrets/initialAdminPassword 2>/dev/null; then
        log_info "请使用上述密码登录Jenkins"
    else
        log_warn "无法获取初始密码，请稍后手动获取："
        echo "docker exec jenkins cat /var/jenkins_home/secrets/initialAdminPassword"
    fi
}

# 显示访问信息
show_access_info() {
    log_info "=== Jenkins 部署完成 ==="
    echo ""
    echo "访问地址: http://$(hostname -I | awk '{print $1}'):8080"
    echo "或者: http://localhost:8080"
    echo ""
    echo "常用命令："
    echo "  查看日志: docker-compose logs -f jenkins"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
    echo "  获取初始密码: docker exec jenkins cat /var/jenkins_home/secrets/initialAdminPassword"
    echo ""
}

# 主函数
main() {
    log_info "开始部署Jenkins..."
    
    check_privileges
    check_docker
    check_docker_compose
    create_directories
    configure_firewall
    start_jenkins $1
    get_admin_password
    show_access_info
    
    log_info "Jenkins 部署脚本执行完成！"
}

# 脚本使用说明
usage() {
    echo "用法: $0 [simple]"
    echo "  simple: 使用简化配置部署Jenkins"
    echo "  默认: 使用完整配置部署Jenkins"
}

# 参数处理
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    usage
    exit 0
fi

# 执行主函数
main $1
