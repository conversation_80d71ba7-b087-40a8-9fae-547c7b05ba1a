# Jenkins Docker Compose 部署指南

## 📋 概述

本项目提供了一个完整的 Jenkins Docker Compose 部署方案，专为 Rocky Linux 系统优化。包含两种部署模式：

- **完整模式**: Jenkins 主节点 + 代理节点 + Nginx 反向代理
- **简化模式**: 仅 Jenkins 主节点

## 🔧 前置要求

### 系统要求
- Rocky Linux 8.x 或 9.x
- 最少 4GB RAM（推荐 8GB）
- 最少 20GB 磁盘空间
- 网络连接正常

### 软件依赖
- Docker CE 20.10+
- Docker Compose 2.0+
- Git
- curl/wget

## 🚀 快速部署

### 步骤 1: 安装依赖环境

```bash
# 1. 克隆项目（如果还没有）
git clone <your-repo-url>
cd demoTest

# 2. 给脚本添加执行权限
chmod +x install-dependencies.sh
chmod +x deploy-jenkins.sh
chmod +x jenkins-manager.sh

# 3. 安装 Docker 和 Docker Compose
sudo ./install-dependencies.sh

# 4. 重新登录或应用用户组权限
newgrp docker
```

### 步骤 2: 部署 Jenkins

```bash
# 方式 1: 完整部署（推荐）
./deploy-jenkins.sh

# 方式 2: 简化部署
./deploy-jenkins.sh simple
```

### 步骤 3: 访问 Jenkins

1. **获取初始管理员密码**：
   ```bash
   # 方法 1: 使用管理脚本
   ./jenkins-manager.sh password
   
   # 方法 2: 直接从容器获取
   docker exec jenkins cat /var/jenkins_home/secrets/initialAdminPassword
   ```

2. **访问 Jenkins Web 界面**：
   - 直接访问: `http://YOUR_SERVER_IP:8080`
   - 通过 Nginx（完整模式）: `http://YOUR_SERVER_IP`

3. **完成初始化设置**：
   - 输入初始管理员密码
   - 选择"安装推荐插件"
   - 创建管理员用户

## 📁 项目结构

```
demoTest/
├── docker-compose.yml              # 完整配置文件
├── docker-compose-simple.yml       # 简化配置文件
├── deploy-jenkins.sh               # 部署脚本
├── jenkins-manager.sh              # 管理脚本
├── install-dependencies.sh         # 依赖安装脚本
├── nginx/
│   ├── nginx.conf                  # Nginx 配置
│   ├── logs/                       # Nginx 日志目录
│   └── ssl/                        # SSL 证书目录
└── jenkins-backup/                 # 备份目录
```

## ⚙️ 配置详解

### 完整模式配置 (docker-compose.yml)

**Jenkins 主节点特性:**
- 镜像: `jenkins/jenkins:lts-jdk11`
- 端口: 8080 (Web UI), 50000 (代理连接)
- 内存: 1-3GB 动态分配
- 数据持久化: `/opt/jenkins/jenkins_home`
- Docker-in-Docker 支持

**Jenkins 代理节点:**
- 自动连接主节点
- 支持 Docker 构建
- 独立工作空间

**Nginx 反向代理:**
- HTTP/HTTPS 支持
- WebSocket 代理
- 日志记录
- SSL 终止（可选）

### 简化模式配置 (docker-compose-simple.yml)

**基础 Jenkins 配置:**
- 仅包含 Jenkins 主节点
- 适合小型项目或测试环境
- 资源占用更少

## 🛠️ 管理命令

使用 `jenkins-manager.sh` 脚本进行日常管理：

```bash
# 查看服务状态
./jenkins-manager.sh status

# 启动服务
./jenkins-manager.sh start

# 停止服务
./jenkins-manager.sh stop

# 重启服务
./jenkins-manager.sh restart

# 查看日志
./jenkins-manager.sh logs

# 进入容器
./jenkins-manager.sh shell

# 获取初始密码
./jenkins-manager.sh password

# 备份数据
./jenkins-manager.sh backup

# 恢复数据
./jenkins-manager.sh restore <backup-file>

# 更新 Jenkins
./jenkins-manager.sh update

# 清理系统
./jenkins-manager.sh cleanup

# 查看系统信息
./jenkins-manager.sh info
```

## 🔐 安全配置

### 防火墙设置

Rocky Linux 防火墙端口配置：
```bash
# Jenkins Web UI
sudo firewall-cmd --permanent --add-port=8080/tcp

# Jenkins 代理端口
sudo firewall-cmd --permanent --add-port=50000/tcp

# HTTP/HTTPS (Nginx)
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp

# 重新加载配置
sudo firewall-cmd --reload
```

### SSL 配置（可选）

1. **生成自签名证书**:
   ```bash
   mkdir -p nginx/ssl
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout nginx/ssl/key.pem \
     -out nginx/ssl/cert.pem
   ```

2. **修改 nginx.conf**: 取消注释 HTTPS 服务器配置块

### 用户权限

Jenkins 容器以 root 用户运行以支持 Docker-in-Docker，生产环境中建议：
- 使用专用的 Jenkins 用户
- 限制 Docker socket 访问权限
- 定期更新镜像和系统

## 💾 数据备份与恢复

### 自动备份

```bash
# 立即备份
./jenkins-manager.sh backup

# 设置定时备份（添加到 crontab）
0 2 * * * /path/to/jenkins-manager.sh backup
```

### 数据恢复

```bash
# 列出备份文件
ls jenkins-backup/

# 恢复指定备份
./jenkins-manager.sh restore backup-20231201-120000.tar.gz
```

### 重要数据位置

- **Jenkins 配置**: `/opt/jenkins/jenkins_home`
- **插件数据**: `/opt/jenkins/jenkins_home/plugins`
- **任务配置**: `/opt/jenkins/jenkins_home/jobs`
- **系统配置**: `/opt/jenkins/jenkins_home/config.xml`

## 🔧 故障排除

### 常见问题

1. **端口被占用**:
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep :8080
   
   # 修改 docker-compose.yml 中的端口映射
   ```

2. **权限问题**:
   ```bash
   # 检查目录权限
   ls -la /opt/jenkins/
   
   # 修复权限
   sudo chown -R 1000:1000 /opt/jenkins/jenkins_home
   ```

3. **内存不足**:
   ```bash
   # 检查系统资源
   free -h
   docker stats
   
   # 调整 JAVA_OPTS 内存设置
   ```

4. **Docker 连接问题**:
   ```bash
   # 检查 Docker 服务
   sudo systemctl status docker
   
   # 检查用户组
   groups $USER
   ```

### 日志查看

```bash
# Jenkins 应用日志
docker-compose logs -f jenkins

# Nginx 访问日志
tail -f nginx/logs/access.log

# 系统日志
sudo journalctl -u docker.service -f
```

## 📈 性能优化

### JVM 调优

在 `docker-compose.yml` 中调整 `JAVA_OPTS`:

```yaml
environment:
  - JAVA_OPTS=-Xmx4g -Xms2g -XX:+UseG1GC -XX:+UseStringDeduplication
```

### 插件管理

建议安装的核心插件：
- Pipeline
- Git
- Docker Pipeline
- Blue Ocean
- Build Timeout
- Workspace Cleanup

### 资源监控

```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
iotop
```

## 🔄 更新维护

### 定期维护任务

```bash
# 更新 Jenkins 镜像
./jenkins-manager.sh update

# 清理无用的 Docker 资源
./jenkins-manager.sh cleanup

# 备份重要数据
./jenkins-manager.sh backup
```

### 版本升级

1. 备份当前数据
2. 更新 docker-compose.yml 中的镜像版本
3. 重新部署服务
4. 验证功能正常

## 📞 支持与帮助

### 获取帮助

```bash
# 脚本帮助
./jenkins-manager.sh help
./deploy-jenkins.sh --help
```

### 有用的链接

- [Jenkins 官方文档](https://www.jenkins.io/doc/)
- [Docker 官方文档](https://docs.docker.com/)
- [Rocky Linux 官方文档](https://docs.rockylinux.org/)

---

**注意**: 本部署方案适用于开发和测试环境。生产环境部署请根据具体需求进行安全加固和性能优化。 