#!/bin/bash

# Rocky Linux Docker 和 Docker Compose 安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统版本
check_system() {
    log_info "检查系统版本..."
    if [[ -f /etc/rocky-release ]]; then
        ROCKY_VERSION=$(cat /etc/rocky-release)
        log_info "检测到系统: $ROCKY_VERSION"
    else
        log_error "这不是 Rocky Linux 系统"
        exit 1
    fi
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    sudo dnf update -y
    sudo dnf install -y epel-release
}

# 安装 Docker
install_docker() {
    log_info "安装 Docker..."
    
    # 添加 Docker 官方仓库
    sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    
    # 安装 Docker
    sudo dnf install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # 启动并启用 Docker 服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到 docker 组
    sudo usermod -aG docker $USER
    
    log_info "Docker 安装完成"
    log_warn "请注销并重新登录以应用 docker 组权限，或运行 'newgrp docker'"
}

# 安装 Docker Compose (独立版本)
install_docker_compose() {
    log_info "安装 Docker Compose..."
    
    # 获取最新版本号
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    # 下载并安装 Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 添加执行权限
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_info "Docker Compose 安装完成"
}

# 安装其他有用工具
install_utilities() {
    log_info "安装其他工具..."
    sudo dnf install -y curl wget git vim htop tree
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 添加 Jenkins 相关端口
    sudo firewall-cmd --permanent --add-port=8080/tcp   # Jenkins Web UI
    sudo firewall-cmd --permanent --add-port=50000/tcp  # Jenkins 代理端口
    sudo firewall-cmd --permanent --add-port=80/tcp     # HTTP
    sudo firewall-cmd --permanent --add-port=443/tcp    # HTTPS
    
    # 重新加载防火墙配置
    sudo firewall-cmd --reload
    
    log_info "防火墙配置完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    echo "Docker 版本:"
    docker --version
    
    echo "Docker Compose 版本:"
    docker-compose --version
    
    echo "Docker 服务状态:"
    sudo systemctl status docker --no-pager -l
}

# 主函数
main() {
    log_info "=== Rocky Linux Docker 环境安装脚本 ==="
    
    check_system
    update_system
    install_docker
    install_docker_compose
    install_utilities
    configure_firewall
    verify_installation
    
    log_info "=== 安装完成 ==="
    echo ""
    echo "下一步："
    echo "1. 重新登录或运行 'newgrp docker' 应用用户组权限"
    echo "2. 运行 './deploy-jenkins.sh' 部署 Jenkins"
    echo "3. 或者运行 './deploy-jenkins.sh simple' 使用简化配置"
}

# 执行主函数
main 