# Jenkins Docker Compose 部署指南

## 在 Rocky Linux 上部署 Jenkins

### 1. 环境准备

#### 1.1 安装 Docker
```bash
# 更新系统
sudo dnf update -y

# 安装 Docker
sudo dnf install -y docker

# 启动并启用 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到 docker 组
sudo usermod -aG docker $USER

# 重新登录或执行以下命令使组权限生效
newgrp docker
```

#### 1.2 安装 Docker Compose
```bash
# 下载 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 2. 部署 Jenkins

#### 2.1 快速部署（推荐新手）
```bash
# 给脚本添加执行权限
chmod +x deploy-jenkins.sh

# 使用简化配置部署
./deploy-jenkins.sh simple
```

#### 2.2 完整部署（包含代理和Nginx）
```bash
# 使用完整配置部署
./deploy-jenkins.sh
```

#### 2.3 手动部署
```bash
# 创建必要目录
sudo mkdir -p /opt/jenkins/jenkins_home
sudo chown -R 1000:1000 /opt/jenkins/jenkins_home

# 启动服务（简化版）
docker-compose -f docker-compose-simple.yml up -d

# 或启动完整版
docker-compose up -d
```

### 3. 配置防火墙

```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=8080/tcp  # Jenkins Web UI
sudo firewall-cmd --permanent --add-port=50000/tcp # Jenkins 代理
sudo firewall-cmd --permanent --add-port=80/tcp    # HTTP
sudo firewall-cmd --permanent --add-port=443/tcp   # HTTPS
sudo firewall-cmd --reload
```

### 4. 访问 Jenkins

#### 4.1 获取访问地址
```bash
# 获取服务器IP
ip addr show | grep inet

# Jenkins访问地址
http://YOUR_SERVER_IP:8080
```

#### 4.2 获取初始管理员密码
```bash
# 方法1：使用管理脚本
./jenkins-manager.sh password

# 方法2：直接从容器获取
docker exec jenkins cat /var/jenkins_home/secrets/initialAdminPassword
```

### 5. Jenkins 管理

#### 5.1 使用管理脚本
```bash
# 给管理脚本添加执行权限
chmod +x jenkins-manager.sh

# 查看服务状态
./jenkins-manager.sh status

# 查看日志
./jenkins-manager.sh logs

# 重启服务
./jenkins-manager.sh restart

# 备份数据
./jenkins-manager.sh backup

# 查看所有可用命令
./jenkins-manager.sh help
```

#### 5.2 常用 Docker Compose 命令
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f jenkins

# 查看服务状态
docker-compose ps
```

### 6. 初始配置

#### 6.1 首次登录设置
1. 访问 `http://YOUR_SERVER_IP:8080`
2. 输入初始管理员密码
3. 选择"安装推荐的插件"
4. 创建管理员用户
5. 配置Jenkins URL

#### 6.2 推荐安装的插件
- Git Plugin
- Pipeline Plugin
- Docker Plugin
- Blue Ocean
- Chinese Localization Plugin

### 7. 故障排除

#### 7.1 常见问题

**问题1：Jenkins 无法启动**
```bash
# 检查日志
docker-compose logs jenkins

# 检查端口占用
sudo netstat -tlnp | grep 8080

# 检查磁盘空间
df -h
```

**问题2：权限问题**
```bash
# 修复Jenkins数据目录权限
sudo chown -R 1000:1000 /opt/jenkins/jenkins_home
sudo chmod -R 755 /opt/jenkins/jenkins_home
```

**问题3：内存不足**
```bash
# 检查内存使用
free -h

# 调整Java堆内存（在docker-compose.yml中）
JAVA_OPTS=-Xmx1024m -Xms512m
```

#### 7.2 性能优化

**调整JVM参数**
```yaml
environment:
  - JAVA_OPTS=-Xmx4096m -Xms2048m -XX:+UseG1GC
```

**增加资源限制**
```yaml
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2.0'
```

### 8. 备份和恢复

#### 8.1 自动备份
```bash
# 创建备份脚本
./jenkins-manager.sh backup

# 设置定时备份（crontab）
0 2 * * * /path/to/jenkins-manager.sh backup
```

#### 8.2 数据恢复
```bash
# 恢复备份
./jenkins-manager.sh restore backup-file.tar.gz
```

### 9. 安全建议

1. **更改默认端口**：修改docker-compose.yml中的端口映射
2. **使用HTTPS**：配置SSL证书
3. **设置防火墙**：只开放必要端口
4. **定期备份**：设置自动备份策略
5. **更新镜像**：定期更新Jenkins镜像

### 10. 监控和维护

```bash
# 查看资源使用情况
./jenkins-manager.sh info

# 清理未使用的Docker资源
./jenkins-manager.sh cleanup

# 更新Jenkins
./jenkins-manager.sh update
```

## 联系支持

如果遇到问题，请检查：
1. Docker和Docker Compose是否正确安装
2. 防火墙设置是否正确
3. 系统资源是否充足
4. 日志文件中的错误信息
