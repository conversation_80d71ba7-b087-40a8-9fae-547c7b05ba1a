version: '3.8'

services:
  jenkins:
    image: jenkins/jenkins:lts-jdk11
    container_name: jenkins-server
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "8080:8080"      # Jenkins Web UI 端口
      - "50000:50000"    # Jenkins 代理端口
    
    # 环境变量配置
    environment:
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false
      - JENKINS_OPTS=--httpPort=8080
    
    # 数据卷挂载
    volumes:
      - jenkins_home:/var/jenkins_home          # Jenkins 主目录持久化
      - /var/run/docker.sock:/var/run/docker.sock  # Docker socket（用于在Jenkins中运行Docker命令）
      - ./jenkins-data:/var/jenkins_home/workspace # 工作空间映射到本地
    
    # 网络配置
    networks:
      - jenkins-network
    
    # 用户配置（解决权限问题）
    user: root
    
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/login || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Jenkins 代理节点（可选）
  jenkins-agent:
    image: jenkins/inbound-agent:latest
    container_name: jenkins-agent
    restart: unless-stopped
    
    environment:
      - JENKINS_URL=http://jenkins:8080
      - JENKINS_SECRET=${JENKINS_AGENT_SECRET}  # 需要从Jenkins获取
      - JENKINS_AGENT_NAME=docker-agent
    
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - jenkins_agent_workspace:/home/<USER>/agent
    
    networks:
      - jenkins-network
    
    depends_on:
      - jenkins

  # Nginx 反向代理（可选，用于HTTPS和域名访问）
  nginx:
    image: nginx:alpine
    container_name: jenkins-nginx
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    
    networks:
      - jenkins-network
    
    depends_on:
      - jenkins

# 网络配置
networks:
  jenkins-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  jenkins_home:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./jenkins-home
  
  jenkins_agent_workspace:
    driver: local
