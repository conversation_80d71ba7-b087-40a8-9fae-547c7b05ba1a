version: '3.8'

services:
  jenkins:
    image: jenkins/jenkins:lts-jdk11
    container_name: jenkins
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "8080:8080"
      - "50000:50000"
    
    # 环境变量
    environment:
      - JAVA_OPTS=-Xmx2048m -Xms1024m
      - TZ=Asia/Shanghai
    
    # 数据持久化
    volumes:
      - jenkins_data:/var/jenkins_home
      - /var/run/docker.sock:/var/run/docker.sock
    
    # 用户权限
    user: root

# 数据卷
volumes:
  jenkins_data:
    driver: local
